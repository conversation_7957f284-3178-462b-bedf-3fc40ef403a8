/* Enhanced Project Detail Page Styling */

/* ===== PROJECT OVERVIEW SECTION ===== */
.project-details-overview {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.project-details-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.project-details-overview .container {
  position: relative;
  z-index: 2;
}

.project-excerpt {
  font-size: 20px;
  line-height: 1.8;
  color: #475569;
  margin-bottom: 30px;
  font-weight: 400;
}

/* Enhanced Project Tags */
.project-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin-top: 30px;
}

.project-tags .tag-item {
  background: linear-gradient(135deg, #03276e 0%, #1e40af 100%);
  color: white;
  padding: 8px 18px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(3, 39, 110, 0.2);
  position: relative;
  overflow: hidden;
}

.project-tags .tag-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.project-tags .tag-item:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(3, 39, 110, 0.3);
}

.project-tags .tag-item:hover::before {
  left: 100%;
}

/* ===== PROJECT META ITEMS ===== */
.project-meta-item {
  background: white;
  border-radius: 20px;
  padding: 35px 25px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(3, 39, 110, 0.05);
  position: relative;
  overflow: hidden;
}

.project-meta-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #03276e, #e89d1a, #03276e);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.project-meta-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.project-meta-item:hover::before {
  transform: scaleX(1);
}

.project-meta-item .icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #03276e 0%, #1e40af 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(3, 39, 110, 0.2);
}

.project-meta-item:hover .icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 35px rgba(3, 39, 110, 0.3);
}

.project-meta-item .icon i {
  font-size: 28px;
  color: white;
}

.project-meta-item h4 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #1e293b;
}

.project-meta-item p {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 0;
  line-height: 1.6;
}

/* ===== ENHANCED STATS SECTION ===== */
.project-stats-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
}

.project-stats-section .stats-item {
  background: white;
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(3, 39, 110, 0.1);
  position: relative;
  overflow: hidden;
}

.project-stats-section .stats-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #03276e, #e89d1a, #03276e);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.project-stats-section .stats-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.project-stats-section .stats-item:hover::before {
  transform: scaleX(1);
}

.project-stats-section .stats-number {
  margin-bottom: 20px;
}

.project-stats-section .stats-number .counter {
  font-size: 56px;
  font-weight: 800;
  background: linear-gradient(135deg, #03276e 0%, #1e40af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  display: inline-block;
}

.project-stats-section .stats-item h4 {
  font-size: 18px;
  font-weight: 600;
  color: #334155;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== ENHANCED FEATURES SECTION ===== */
.project-features-section {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  position: relative;
}

.feature-item {
  background: white;
  border-radius: 20px;
  padding: 35px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(3, 39, 110, 0.05);
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(3, 39, 110, 0.02) 0%, rgba(232, 157, 26, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.feature-item:hover::before {
  opacity: 1;
}

.feature-item .icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #03276e 0%, #1e40af 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6px 20px rgba(3, 39, 110, 0.2);
}

.feature-item:hover .icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-item .icon i {
  font-size: 24px;
  color: white;
}

.feature-item h4 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 15px;
  color: #1e293b;
}

.feature-item p {
  color: #64748b;
  line-height: 1.7;
  margin-bottom: 0;
}

/* ===== ENHANCED TIMELINE SECTION ===== */
.project-timeline-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.timeline-item {
  background: white;
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(3, 39, 110, 0.05);
  position: relative;
  overflow: hidden;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #03276e, #e89d1a);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.timeline-item:hover {
  transform: translateX(10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.timeline-item:hover::before {
  transform: scaleY(1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .project-excerpt {
    font-size: 18px;
  }
  
  .project-meta-item {
    padding: 30px 20px;
    margin-bottom: 20px;
  }
  
  .project-meta-item .icon {
    width: 60px;
    height: 60px;
  }
  
  .project-meta-item .icon i {
    font-size: 24px;
  }
  
  .project-stats-section .stats-number .counter {
    font-size: 48px;
  }
  
  .feature-item {
    padding: 25px;
    margin-bottom: 20px;
  }
  
  .timeline-item {
    padding: 25px;
    margin-bottom: 20px;
  }
}

@media (max-width: 576px) {
  .project-tags {
    justify-content: center;
  }
  
  .project-tags .tag-item {
    font-size: 12px;
    padding: 6px 14px;
  }
  
  .project-meta-item {
    padding: 25px 15px;
  }
  
  .project-stats-section .stats-number .counter {
    font-size: 40px;
  }
}
